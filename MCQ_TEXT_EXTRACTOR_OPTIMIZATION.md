# MCQ Text Extractor API Optimization

## Overview

This document describes the optimization of the `/api/mcq-text-extractor` API to implement a task-based system with background processing and polling, using the existing `wslog.request_tracker` database table.

## Implementation Details

### 1. Database Integration

**Service**: `services/request_tracker_service.py`
- Uses raw SQL queries (following the pattern from `agents/mcq_extract_log.py`)
- Connects to `wslog.request_tracker` table using the LOG_SCHEMA
- Implements CRUD operations for task tracking

**Table Fields Used**:
- `date_created`: Task creation timestamp
- `error`: Error flag (1 for failed tasks, NULL otherwise)
- `error_message`: Error message for failed tasks, or JSON result data for completed tasks
- `last_updated`: Last update timestamp
- `request_id`: Unique task identifier (UUID)
- `status`: Task status ('STARTED', 'IN_PROGRESS', 'COMPLETED', 'FAILED')

### 2. API Changes

**Modified Endpoint**: `/api/mcq-text-extractor`
- Now returns immediately with a task ID
- Starts background processing using `asyncio.create_task()`
- Response format:
  ```json
  {
    "status": "started",
    "task_id": "uuid-here",
    "message": "MCQ text extraction started in background"
  }
  ```

**New Endpoint**: `/api/mcq-text-extractor/status`
- Accepts POST requests with `task_id`
- Returns current task status
- Automatically cleans up completed tasks after returning results
- Response formats:
  - In Progress: `{"status": "IN_PROGRESS", "task_id": "...", ...}`
  - Completed: `{"status": "COMPLETED", "task_id": "...", "result": {...}, ...}`
  - Failed: `{"status": "FAILED", "task_id": "...", "error_message": "...", ...}`

### 3. Background Processing

**Function**: `process_mcq_text_extraction_background()`
- Runs the actual MCQ text extraction in the background
- Updates task status throughout the process:
  - `STARTED` → `IN_PROGRESS` → `COMPLETED`/`FAILED`
- Stores result data in the `error_message` field as JSON for completed tasks
- Handles exceptions and updates status to `FAILED` with error details

### 4. Frontend Changes

**File**: `web/templates/mcq_text_extractor.html`

**New Polling Function**: `pollTaskStatus(taskId)`
- Polls every 5 seconds
- Maximum 30 minutes timeout (360 polls)
- Handles all task states appropriately
- Stops timer and displays results when completed
- Shows appropriate error messages for failures

**Modified Extraction Flow**:
1. Submit form → Get task ID immediately
2. Start timer and polling
3. Poll status every 5 seconds
4. Display results when completed
5. Stop timer and clean up

### 5. Task Cleanup

**Automatic Cleanup**:
- Completed tasks are deleted immediately after results are returned
- Background cleanup service runs every 12 hours
- Removes tasks older than 24 hours

**Manual Cleanup**:
- `RequestTrackerService.clean_old_tasks(max_age_hours=24)`
- Returns count of cleaned tasks

## API Usage Examples

### 1. Start MCQ Text Extraction

```bash
curl -X POST http://localhost:8000/api/mcq-text-extractor \
  -H "Content-Type: application/json" \
  -d '{
    "resId": "123",
    "total_questions": 50,
    "explanation_start_page": 5,
    "username": "testuser"
  }'
```

Response:
```json
{
  "status": "started",
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "message": "MCQ text extraction started in background"
}
```

### 2. Check Task Status

```bash
curl -X POST http://localhost:8000/api/mcq-text-extractor/status \
  -H "Content-Type: application/json" \
  -d '{
    "task_id": "550e8400-e29b-41d4-a716-446655440000"
  }'
```

Response (In Progress):
```json
{
  "status": "IN_PROGRESS",
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "created_at": "2024-01-01T10:00:00",
  "last_updated": "2024-01-01T10:05:00"
}
```

Response (Completed):
```json
{
  "status": "COMPLETED",
  "task_id": "550e8400-e29b-41d4-a716-446655440000",
  "message": "MCQ text extraction completed successfully",
  "completed_at": "2024-01-01T10:15:00",
  "result": {
    "status": "success",
    "chapter_id": "456",
    "resource_id": "123",
    "s3_path": "path/to/result.txt",
    "total_images": 25,
    "text_files_created": 25
  }
}
```

## Benefits

1. **Non-blocking**: API returns immediately, allowing users to continue other work
2. **Real-time Updates**: Frontend shows progress through polling
3. **Robust Error Handling**: Failed tasks are properly tracked and reported
4. **Automatic Cleanup**: Prevents database bloat with old task records
5. **Scalable**: Can handle multiple concurrent extractions
6. **User-Friendly**: Timer shows elapsed time, clear status messages

## Testing

Run the test script to verify the implementation:

```bash
python test_request_tracker.py
```

This tests all CRUD operations and verifies the database integration works correctly.

## Files Modified/Created

### Created:
- `services/request_tracker_service.py` - Main service for task tracking
- `test_request_tracker.py` - Test script for verification
- `MCQ_TEXT_EXTRACTOR_OPTIMIZATION.md` - This documentation

### Modified:
- `api/api.py` - Added background processing and polling endpoint
- `models/api_models.py` - Added TaskStatusRequest model
- `web/templates/mcq_text_extractor.html` - Implemented polling frontend
- `main.py` - Added request tracker cleanup to background tasks

## Configuration

The system uses these configurable parameters:
- **Polling Interval**: 5 seconds (frontend)
- **Max Polling Time**: 30 minutes (frontend)
- **Cleanup Interval**: 12 hours (background)
- **Task Retention**: 24 hours before cleanup
