# models/api_models.py

from pydantic import BaseModel
from typing import Optional


class Item(BaseModel):
    """
    Pydantic model for an Item.
    It contains a file in bytes and an optional filename.

    Attributes:
        file (bytes): The file data in bytes.
        filename (Optional[str]): The name of the file.
    """
    file: bytes
    filename: Optional[str]


class Query(BaseModel):
    """
    Pydantic model for a Query.
    It contains a query string, a namespace string, and a resType string.

    Attributes:
        query (str): The query string.
        namespace (str): The namespace string.
        resType (str): The resType string.
    """
    query: str
    namespace: str
    resType: str
    chatHistory: Optional[list] = None
    chatHistoryApp: Optional[str] = None


class AdminQuery(BaseModel):
    """
    Pydantic model for an AdminQuery.
    It contains a query string, a namespace string, a resType string, and a username string.

    Attributes:
        query (str): The query string.
        namespace (str): The namespace string.
        resType (str): The resType string.
        username (str): The username string.
        readingMaterialResId (str): The readingMaterialResId string.
    """
    query: str
    namespace: str
    resType: str
    username: str
    readingMaterialResId: Optional[str] = None


class AdminQueryNew(BaseModel):
    """
    Pydantic model for an AdminQuery.
    It contains a query string, a namespace string, a resType string, and a username string.

    Attributes:
        query (str): The query string.
        namespace (str): The namespace string.
        resType (str): The resType string.
        username (str): The username string.
        filePath (str): The path of the file to read
        readingMaterialResId (str): The readingMaterialResId string.
    """
    query: str
    namespace: str
    resType: str
    username: str
    filePath: str
    readingMaterialResId: Optional[str] = None


class DeleteNs(BaseModel):
    """
    Pydantic model for an AdminQuery.
    It contains a query string, a namespace string, a resType string, and a username string.

    Attributes:
        count (int)
        index (str)
        usertype (str)
    """
    count: int
    index: str
    usertype: str


class ProcessPDFQuery(BaseModel):
    """
    Pydantic model for an ProcessPDFQuery.
    It contains a namespace string, a filepath string.

    Attributes:
        namespace (str): The namespace string.
        filePath (str): The path of the file to read
    """
    namespace: str
    filePath: str


class ProcessPDFExtQuery(BaseModel):
    """
    Pydantic model for an ProcessPDFQuery.
    It contains a namespace string, a filepath string.

    Attributes:
        bookId (str): The namespace string.
        filePath (str): The path of the file to read
    """
    resId: str


class ExtractRequest(BaseModel):
    bookId: str


class ExtractResourceQuery(BaseModel):
    """
    Pydantic model for extracting a resource by ID.

    Attributes:
        resId (str): The resource ID to extract.
        showProcessStatus (bool): Whether to show real-time process status updates.
    """
    resId: str
    showProcessStatus: bool = False


class TaskStatusQuery(BaseModel):
    """
    Pydantic model for getting task status.

    Attributes:
        task_id (str): The task ID to get status for.
    """
    task_id: str


class PDFTextExtractQuery(BaseModel):
    """
    Pydantic model for extracting text from a PDF resource by ID.

    Attributes:
        resId (str): The resource ID to extract text from.
        showProcessStatus (bool): Whether to show real-time process status updates.
    """
    resId: str


class PDFTextExtractByChapterQuery(BaseModel):
    """
    Pydantic model for extracting text from a PDF resource by chapter ID.

    Attributes:
        chapterId (str): The chapter ID to extract text from.
    """
    chapterId: str


class PDFTextExtractByBookQuery(BaseModel):
    """
    Pydantic model for extracting text from all PDF resources in a book.

    Attributes:
        bookId (str): The book ID to extract text from.
    """
    bookId: str


class ChapterInfo(BaseModel):
    """
    Pydantic model for chapter information.

    Attributes:
        id (str): The chapter ID.
        name (str): The chapter name.
        book_id (str): The book ID.
    """
    id: str
    name: str
    book_id: str


class ChatImageRequest(BaseModel):
    message: str
    image: Optional[str] = None


class MCQExtractQuery(BaseModel):
    """
    Pydantic model for extracting MCQs from a PDF resource by ID.

    Attributes:
        res_id (str): The resource ID to extract MCQs from.
    """
    res_id: str


class MCQExtractValidateQuery(BaseModel):
    """
    Pydantic model for extracting and validating MCQs from a PDF resource by ID.

    Attributes:
        res_id (str): The resource ID to extract and validate MCQs from.
        from_ws (bool): Flag indicating if the request is from WS (Wonderslate). If True, mcqs array will be removed from the response.
    """
    res_id: str
    from_ws: bool = False
    username: str = "system"


class TOCExtractQuery(BaseModel):
    """
    Pydantic model for extracting table of contents from a PDF file.

    Attributes:
        pdf_file_path (Optional[str]): The path to the PDF file if already uploaded.
    """
    pdf_file_path: Optional[str] = None


class TOCExtractByResourceQuery(BaseModel):
    """
    Pydantic model for extracting table of contents from a PDF by S3 path.

    Attributes:
        s3_path (str): The S3 path to the PDF file.
        toc_page_numbers (Optional[str]): Comma-separated list of page numbers containing TOC.
        starting_page_number (Optional[int]): Starting page number for the PDF.
    """
    s3_path: str
    toc_page_numbers: Optional[str] = None
    starting_page_number: Optional[int] = None


class MCQTextExtractQuery(BaseModel):
    """
    Pydantic model for extracting text content from MCQ images.

    Attributes:
        resId (str): The resource ID to extract text from.
        force_reextract (bool): Whether to force re-extraction even if file exists.
        total_questions (int): Total number of questions for MCQ parsing batches.
        explanation_start_page (int): Page number from which explanations start (default: 5).
    """
    resId: str
    force_reextract: bool = False
    total_questions: int
    explanation_start_page: int = 5
    username: str = "system"


class TaskStatusRequest(BaseModel):
    """
    Pydantic model for checking task status.

    Attributes:
        task_id (str): The task ID to check status for.
    """
    task_id: str


class MCQTranslatorQuery(BaseModel):
    """
    Pydantic model for translating MCQ content from one language to another.

    Attributes:
        resId (str): The resource ID to translate MCQs from.
        total_questions (int): Total number of questions for MCQ parsing batches.
        source_language (str): The source language of the MCQ content.
        destination_language (str): The target language to translate the MCQ content to.
        username (str): Username of the user performing the translation.
    """
    resId: str
    total_questions: int
    source_language: str
    destination_language: str
    username: str = "system"


class MCQTranslatorFileQuery(BaseModel):
    """
    Pydantic model for translating MCQ content from uploaded PDF file.

    Attributes:
        total_questions (int): Total number of questions for MCQ parsing batches.
        explanation_start_page (int): Page number from which explanations start.
        source_language (str): The source language of the MCQ content.
        destination_language (str): The target language to translate the MCQ content to.
        username (str): Username of the user performing the translation.
    """
    total_questions: int
    source_language: str
    destination_language: str
    username: str = "system"


class MCQTranslatorDownloadQuery(BaseModel):
    """
    Pydantic model for downloading translated MCQ content.

    Attributes:
        translation_id (str): The UUID of the translation process.
    """
    translation_id: str
