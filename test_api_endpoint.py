#!/usr/bin/env python3
"""
Test script to verify the MCQ text extractor API returns immediately.
"""

import asyncio
import aiohttp
import time
import json

async def test_api_endpoint():
    """Test the MCQ text extractor API endpoint."""
    
    print("Testing MCQ Text Extractor API Endpoint")
    print("=" * 50)
    
    # Test the task creation endpoint
    url = "http://localhost:8000/api/test-task-creation"
    
    try:
        print("\n1. Testing task creation speed...")
        start_time = time.time()
        
        async with aiohttp.ClientSession() as session:
            async with session.post(url) as response:
                end_time = time.time()
                duration = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✓ API responded in {duration:.3f} seconds")
                    print(f"✓ Response: {json.dumps(result, indent=2)}")
                else:
                    print(f"✗ API returned status {response.status}")
                    text = await response.text()
                    print(f"✗ Response: {text}")
                    
    except aiohttp.ClientConnectorError:
        print("✗ Could not connect to server. Make sure the server is running on localhost:8000")
        return
    except Exception as e:
        print(f"✗ Error testing API: {e}")
        return
    
    # Test the actual MCQ text extractor endpoint
    mcq_url = "http://localhost:8000/api/mcq-text-extractor"
    
    try:
        print("\n2. Testing MCQ text extractor endpoint...")
        start_time = time.time()
        
        test_payload = {
            "resId": "test_resource_123",
            "total_questions": 10,
            "explanation_start_page": 5,
            "username": "test_user"
        }
        
        async with aiohttp.ClientSession() as session:
            async with session.post(mcq_url, json=test_payload) as response:
                end_time = time.time()
                duration = end_time - start_time
                
                if response.status == 200:
                    result = await response.json()
                    print(f"✓ MCQ API responded in {duration:.3f} seconds")
                    print(f"✓ Response: {json.dumps(result, indent=2)}")
                    
                    # If we got a task ID, test the status endpoint
                    if result.get("task_id"):
                        await test_status_endpoint(result["task_id"])
                        
                else:
                    print(f"✗ MCQ API returned status {response.status}")
                    text = await response.text()
                    print(f"✗ Response: {text}")
                    
    except Exception as e:
        print(f"✗ Error testing MCQ API: {e}")

async def test_status_endpoint(task_id):
    """Test the status endpoint."""
    
    print(f"\n3. Testing status endpoint for task {task_id}...")
    
    status_url = "http://localhost:8000/api/mcq-text-extractor/status"
    
    try:
        async with aiohttp.ClientSession() as session:
            # Test status check
            status_payload = {"task_id": task_id}
            
            async with session.post(status_url, json=status_payload) as response:
                if response.status == 200:
                    result = await response.json()
                    print(f"✓ Status API responded successfully")
                    print(f"✓ Status: {result.get('status')}")
                    print(f"✓ Task ID: {result.get('task_id')}")
                else:
                    print(f"✗ Status API returned status {response.status}")
                    text = await response.text()
                    print(f"✗ Response: {text}")
                    
    except Exception as e:
        print(f"✗ Error testing status API: {e}")

if __name__ == "__main__":
    asyncio.run(test_api_endpoint())
