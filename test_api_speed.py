#!/usr/bin/env python3
"""
Test script to check API response speed.
"""

import asyncio
import time
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.request_tracker_service import RequestTrackerService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_api_speed():
    """Test the speed of database operations."""
    
    print("Testing API Speed")
    print("=" * 40)
    
    # Initialize the service
    tracker = RequestTrackerService()
    
    try:
        # Test database connection speed
        print("\n1. Testing database connection speed...")
        start_time = time.time()
        
        # Create a task
        task_id = tracker.create_task("speed_test")
        
        end_time = time.time()
        duration = end_time - start_time
        
        if task_id:
            print(f"✓ Task created in {duration:.3f} seconds")
            print(f"✓ Task ID: {task_id}")
            
            # Test status retrieval speed
            start_time = time.time()
            status = tracker.get_task_status(task_id)
            end_time = time.time()
            duration = end_time - start_time
            
            print(f"✓ Status retrieved in {duration:.3f} seconds")
            print(f"✓ Status: {status.get('status')}")
            
            # Clean up
            tracker.delete_task(task_id)
            print("✓ Task cleaned up")
            
        else:
            print("✗ Failed to create task")
            
        # Test multiple rapid operations
        print("\n2. Testing rapid operations...")
        start_time = time.time()
        
        task_ids = []
        for i in range(5):
            task_id = tracker.create_task(f"rapid_test_{i}")
            if task_id:
                task_ids.append(task_id)
        
        end_time = time.time()
        duration = end_time - start_time
        
        print(f"✓ Created {len(task_ids)} tasks in {duration:.3f} seconds")
        print(f"✓ Average: {duration/len(task_ids):.3f} seconds per task")
        
        # Clean up rapid test tasks
        for task_id in task_ids:
            tracker.delete_task(task_id)
        
        print("✓ All rapid test tasks cleaned up")
        
        print("\n" + "=" * 40)
        print("Speed test completed!")
        
    except Exception as e:
        print(f"\n✗ Error during speed test: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_api_speed())
