#!/usr/bin/env python3
"""
Test script for the request tracker service.
"""

import asyncio
import sys
import os

# Add the project root to the Python path
sys.path.insert(0, os.path.dirname(os.path.abspath(__file__)))

from services.request_tracker_service import RequestTrackerService
import logging

# Set up logging
logging.basicConfig(level=logging.INFO)
logger = logging.getLogger(__name__)

async def test_request_tracker():
    """Test the request tracker service functionality."""
    
    print("Testing Request Tracker Service")
    print("=" * 40)
    
    # Initialize the service
    tracker = RequestTrackerService()
    
    try:
        # Test 1: Create a new task
        print("\n1. Creating a new task...")
        task_id = tracker.create_task("test_task")
        
        if task_id:
            print(f"✓ Task created successfully with ID: {task_id}")
        else:
            print("✗ Failed to create task")
            return
        
        # Test 2: Get task status
        print("\n2. Getting task status...")
        status = tracker.get_task_status(task_id)
        print(f"✓ Task status: {status}")
        
        # Test 3: Update task status to IN_PROGRESS
        print("\n3. Updating task status to IN_PROGRESS...")
        success = tracker.update_task_status(task_id, 'IN_PROGRESS')
        
        if success:
            print("✓ Task status updated successfully")
            
            # Get updated status
            status = tracker.get_task_status(task_id)
            print(f"✓ Updated status: {status.get('status')}")
        else:
            print("✗ Failed to update task status")
        
        # Test 4: Update task status to COMPLETED with result data
        print("\n4. Updating task status to COMPLETED...")
        result_data = {
            "status": "success",
            "message": "Test task completed",
            "data": {"test": "value"}
        }
        
        success = tracker.update_task_status(task_id, 'COMPLETED', result_data=result_data)
        
        if success:
            print("✓ Task marked as completed")
            
            # Get final status
            status = tracker.get_task_status(task_id)
            print(f"✓ Final status: {status.get('status')}")
            print(f"✓ Error message field contains: {status.get('error_message')[:100] if status.get('error_message') else 'None'}...")
        else:
            print("✗ Failed to mark task as completed")
        
        # Test 5: Delete the task
        print("\n5. Deleting the task...")
        success = tracker.delete_task(task_id)
        
        if success:
            print("✓ Task deleted successfully")
            
            # Verify deletion
            status = tracker.get_task_status(task_id)
            if status.get("status") == "not_found":
                print("✓ Task deletion verified")
            else:
                print("✗ Task still exists after deletion")
        else:
            print("✗ Failed to delete task")
        
        # Test 6: Test cleanup function
        print("\n6. Testing cleanup function...")
        cleaned = tracker.clean_old_tasks(max_age_hours=0)  # Clean all tasks
        print(f"✓ Cleaned up {cleaned} old tasks")
        
        print("\n" + "=" * 40)
        print("All tests completed!")
        
    except Exception as e:
        print(f"\n✗ Error during testing: {e}")
        import traceback
        traceback.print_exc()

if __name__ == "__main__":
    asyncio.run(test_request_tracker())
